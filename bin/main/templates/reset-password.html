<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title>Reset Password</title>
    <!-- Include Bootstrap CSS -->
    <link href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container">
    <h3>Reset Password</h3>
    <form th:action="@{/user/reset-password}" method="post">
        <input type="hidden" name="token" th:value="${token}" />
        <div class="form-group">
            <label for="newPassword">New Password:</label>
            <input type="password" class="form-control" id="newPassword" name="newPassword" required />
        </div>
        <button type="submit" class="btn btn-primary">Reset Password</button>
    </form>
    <p th:if="${message}" class="alert alert-success" th:text="${message}"></p>
    <p th:if="${error}" class="alert alert-danger" th:text="${error}"></p>
</div>
</body>
</html>

