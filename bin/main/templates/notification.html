<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Blog Platform</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sockjs-client/1.6.1/sockjs.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/stomp.js/2.3.3/stomp.min.js"></script>
    <style>
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #333;
            color: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.5s;
        }
        .toast.show {
            opacity: 1;
        }
    </style>
</head>
<body>
<h1>Blog Platform</h1>
<div id="notifications"></div>

<script>
    // Kết nối WebSocket
    const socket = new SockJS('http://localhost:8080/ws');
    const stompClient = Stomp.over(socket);

    stompClient.connect({}, function(frame) {
        console.log('Connected: ' + frame);

        // Subscribe kênh global (bài viết mới)
        stompClient.subscribe('/topic/global', function(message) {
            const notification = message.body;
            showToast(notification);
        });

        // Subscribe kênh comments (ví dụ cho postId cụ thể)
        const postId = "post1"; // Thay bằng postId thực tế
        stompClient.subscribe(`/topic/comments/${postId}`, function(message) {
            const comment = JSON.parse(message.body);
            showToast(`New comment by ${comment.authorUsername}: ${comment.content}`);
        });

        // Subscribe kênh post mới
        stompClient.subscribe(`/topic/post/${postId}`, function(message) {
            showToast(message.body);
        });
    }, function(error) {
        console.error('Connection error:', error);
    });

    // Hiển thị toast notification
    function showToast(message) {
        const toast = document.createElement('div');
        toast.className = 'toast';
        toast.textContent = message;

        document.getElementById('notifications').appendChild(toast);

        // Hiển thị toast
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        // Ẩn sau 5 giây
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 500); // Xóa sau khi mờ đi
        }, 5000);
    }
</script>
</body>
</html>