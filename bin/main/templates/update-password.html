<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title>Update Password</title>
    <!-- Include Bootstrap CSS -->
    <link href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container">
    <h6>just use for test this feature</h6>

    <h3>Update Password</h3>
    <form th:action="@{/user/update-password}" method="post">
        <div class="form-group">
            <label for="oldPassword">Old Password:</label>
            <input type="password" class="form-control" id="oldPassword" name="oldPassword" required />
        </div>
        <div class="form-group">
            <label for="newPassword">New Password:</label>
            <input type="password" class="form-control" id="newPassword" name="newPassword" required />
        </div>
        <button type="submit" class="btn btn-primary">Update Password</button>
    </form>
    <p th:if="${message}" class="alert alert-success" th:text="${message}"></p>
    <p th:if="${error}" class="alert alert-danger" th:text="${error}"></p>
</div>
</body>
</html>
