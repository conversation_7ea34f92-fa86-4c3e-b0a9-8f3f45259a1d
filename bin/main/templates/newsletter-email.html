<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="'New Article: ' + ${postTitle}">New Article</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .article {
            margin-bottom: 30px;
        }
        .article-title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            line-height: 1.3;
        }
        .article-meta {
            color: #666;
            font-size: 14px;
            margin-bottom: 20px;
        }
        .article-thumbnail {
            width: 100%;
            max-width: 500px;
            height: auto;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .article-content {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        .button {
            display: inline-block;
            padding: 12px 30px;
            background-color: #3498db;
            color: #ffffff;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
        }
        .button:hover {
            background-color: #2980b9;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
        }
        .unsubscribe {
            color: #999;
            font-size: 12px;
            text-decoration: none;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">📰 Blog Platform</div>
            <h1>New Article Published!</h1>
        </div>
        
        <div class="article">
            <p>Hello <span th:text="${subscriberName}">Subscriber</span>,</p>
            
            <p>We have a new article that we think you'll enjoy:</p>
            
            <h2 class="article-title" th:text="${postTitle}">Article Title</h2>
            
            <div class="article-meta">
                <span>By <strong th:text="${authorName}">Author Name</strong></span>
            </div>
            
            <div th:if="${postThumbnail}" style="text-align: center; margin-bottom: 20px;">
                <img th:src="${postThumbnail}" alt="Article thumbnail" class="article-thumbnail" />
            </div>
            
            <div class="article-content">
                <p th:text="${postContent}">Article content preview...</p>
            </div>
            
            <div style="text-align: center;">
                <a th:href="${postUrl}" class="button">Read Full Article</a>
            </div>
            
            <p>We hope you enjoy this article! Stay tuned for more great content.</p>
        </div>
        
        <div class="footer">
            <p>Best regards,<br>The Blog Platform Team</p>
            
            <p>
                <a th:href="${unsubscribeUrl}" class="unsubscribe">
                    Unsubscribe from this newsletter
                </a>
            </p>
            
            <p style="font-size: 12px; color: #999;">
                You received this email because you subscribed to our newsletter.
            </p>
        </div>
    </div>
</body>
</html>
