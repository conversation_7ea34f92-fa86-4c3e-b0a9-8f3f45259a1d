spring.application.name=blog-platform

base-url = localhost:8080
spring.datasource.url=***************************************
spring.datasource.username=root
spring.datasource.password=123456789
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
# Hibernate properties
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
#spring.jpa.show-sql=true
#spring.jpa.properties.hibernate.format_sql=true
#logging.level.org.hibernate.SQL=DEBUG
#logging.level.org.hibernate.type.descriptor.sql.BasicBinder=trace
#jwt config
blog.app.jwtSecret= +M+LJnkHPk0k5ZD+DvYQXNCKCoZCEpGJtssoE+8vBcs=
blog.app.jwtExpirationMs=86400000
blog.app.jwtCookieName = token

blog.openapi.dev-url=http://localhost:8080
blog.openapi.prod-url=https://blog-api.com

spring.mail.host=sandbox.smtp.mailtrap.io
spring.mail.port=2525
spring.mail.username=3970652266de4f
spring.mail.password=009f3527de8a53
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true
spring.mail.properties.mail.smtp.ssl.trust=sandbox.smtp.mailtrap.io
#banner config for nothing
spring.banner.location=nguyen.txt
application.version= v1.0.0
application.title= spring boot blog-platforms application
#telegram
#telegram.bot.token=**********************************************
#telegram.chat.id=-4578983881

spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# Thêm c?u hình ?? ph?c v? file t?nh t? th? m?c uploads
spring.web.resources.static-locations=classpath:/static/,file:uploads/

spring.cache.type=simple

logging.level.org.springframework.web.socket=DEBUG

meme.url = /src/main/resources/meme/
video.storage.path=./videos/
logging.level.org.springframework=INFO
logging.level.com.yourpackage=DEBUG

management.endpoints.web.exposure.include=*
management.endpoints.web.base-path=/actuator

springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui

logging.level.org.springframework.security=DEBUG
#logging.file.name=logs/app.log

server.port=8888